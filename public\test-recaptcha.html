<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>reCAPTCHA Enterprise 测试页面</title>
    <script src="https://www.google.com/recaptcha/api.js?render=6LfdfIErAAAAAGPd9T0wNi37IOhF4oH0jAeObqEU"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            margin-bottom: 20px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>reCAPTCHA Enterprise 测试</h1>
        
        <div class="info">
            <strong>说明：</strong>
            <ul>
                <li>此页面用于测试 Strapi Contact API 的 reCAPTCHA Enterprise 集成</li>
                <li>填写表单并提交，系统会自动获取 reCAPTCHA token 并发送到后端验证</li>
                <li>确保在 .env 文件中配置了正确的 reCAPTCHA Enterprise 密钥</li>
            </ul>
        </div>

        <form id="contactForm">
            <div class="form-group">
                <label for="username">姓名 *</label>
                <input type="text" id="username" name="username" required>
            </div>

            <div class="form-group">
                <label for="email">邮箱 *</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="company">公司</label>
                <input type="text" id="company" name="company">
            </div>

            <div class="form-group">
                <label for="phone">电话</label>
                <input type="tel" id="phone" name="phone">
            </div>

            <div class="form-group">
                <label for="message">消息 *</label>
                <textarea id="message" name="message" required placeholder="请输入您的消息..."></textarea>
            </div>

            <button type="submit" id="submitBtn">提交联系信息</button>
        </form>

        <div id="result" class="result"></div>
    </div>

    <script>
        const STRAPI_URL = 'http://localhost:1337';
        const SITE_KEY = '6LfdfIErAAAAAGPd9T0wNi37IOhF4oH0jAeObqEU';

        document.getElementById('contactForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const resultDiv = document.getElementById('result');
            
            // 禁用提交按钮
            submitBtn.disabled = true;
            submitBtn.textContent = '正在提交...';
            
            try {
                // 获取表单数据
                const formData = new FormData(this);
                const contactData = {
                    username: formData.get('username'),
                    email: formData.get('email'),
                    company: formData.get('company'),
                    phone: formData.get('phone'),
                    message: [
                        {
                            type: 'paragraph',
                            children: [
                                {
                                    type: 'text',
                                    text: formData.get('message')
                                }
                            ]
                        }
                    ]
                };

                // 获取 reCAPTCHA token
                const recaptchaToken = await grecaptcha.execute(SITE_KEY, {
                    action: 'contact_form'
                });

                console.log('reCAPTCHA token obtained:', recaptchaToken.substring(0, 50) + '...');

                // 发送请求到 Strapi API
                const response = await fetch(`${STRAPI_URL}/api/contacts`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        data: contactData,
                        recaptchaToken: recaptchaToken
                    })
                });

                const responseData = await response.json();

                if (response.ok) {
                    // 成功
                    showResult('success', '✅ 联系信息提交成功！', responseData);
                    document.getElementById('contactForm').reset();
                } else {
                    // 失败
                    showResult('error', '❌ 提交失败', responseData);
                }

            } catch (error) {
                console.error('提交错误:', error);
                showResult('error', '❌ 网络错误', { error: error.message });
            } finally {
                // 恢复提交按钮
                submitBtn.disabled = false;
                submitBtn.textContent = '提交联系信息';
            }
        });

        function showResult(type, message, data) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `
                <strong>${message}</strong>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            resultDiv.style.display = 'block';
            
            // 滚动到结果区域
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // 页面加载完成后初始化 reCAPTCHA
        grecaptcha.ready(function() {
            console.log('reCAPTCHA 已准备就绪');
        });
    </script>
</body>
</html>
