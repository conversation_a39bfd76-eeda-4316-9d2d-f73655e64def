# reCAPTCHA 版本对比说明

## 🤔 你遇到的问题

你之前遇到的问题是**混合使用了两种不同的 reCAPTCHA 服务**：

- **前端**：使用标准 reCAPTCHA v3 的 Site Key
- **后端**：使用 reCAPTCHA Enterprise 的 API 和 Google Cloud API Key

这两种服务不兼容，导致验证失败。

## 📊 两种 reCAPTCHA 服务对比

### 1. 标准 reCAPTCHA v3（免费版）✅ 当前使用

| 项目 | 详情 |
|------|------|
| **费用** | 免费 |
| **配置复杂度** | 简单 |
| **前端 Site Key** | `6LfdfIErAAAAAGPd9T0wNi37IOhF4oH0jAeObqEU` |
| **后端 Secret Key** | `6LfdfIErAAAAAOYyaMC7M8yxIz7XNRXWC0Ad-6I_` |
| **验证 URL** | `https://www.google.com/recaptcha/api/siteverify` |
| **请求格式** | `application/x-www-form-urlencoded` |
| **认证方式** | Secret Key |
| **获取方式** | [Google reCAPTCHA 控制台](https://www.google.com/recaptcha/admin) |

**环境变量配置：**
```env
RECAPTCHA_SECRET_KEY=6LfdfIErAAAAAOYyaMC7M8yxIz7XNRXWC0Ad-6I_
RECAPTCHA_SITE_KEY=6LfdfIErAAAAAGPd9T0wNi37IOhF4oH0jAeObqEU
RECAPTCHA_MIN_SCORE=0.5
```

### 2. reCAPTCHA Enterprise（企业版）

| 项目 | 详情 |
|------|------|
| **费用** | 付费（按请求计费） |
| **配置复杂度** | 复杂 |
| **前端 Site Key** | 需要在 Google Cloud Console 创建 |
| **后端认证** | Google Cloud API Key |
| **验证 URL** | `https://recaptchaenterprise.googleapis.com/v1/projects/{PROJECT_ID}/assessments?key={API_KEY}` |
| **请求格式** | `application/json` |
| **认证方式** | Google Cloud API Key 或服务账号 |
| **获取方式** | [Google Cloud Console](https://console.cloud.google.com/) |

**环境变量配置：**
```env
RECAPTCHA_API_KEY=AIzaSyC03FDvfd9dkn0wvpvecdJBEK7efwb-Tzw
RECAPTCHA_PROJECT_ID=huari-project-1752308753071
RECAPTCHA_SITE_KEY=enterprise_site_key_here
RECAPTCHA_MIN_SCORE=0.5
```

## 🎯 推荐方案

### 对于大多数项目：使用标准 reCAPTCHA v3 ✅

**优势：**
- ✅ 完全免费
- ✅ 配置简单
- ✅ 无需 Google Cloud 账号
- ✅ 足够的安全防护
- ✅ 更少的依赖

**适用场景：**
- 中小型网站
- 个人项目
- 预算有限的项目
- 不需要高级分析功能

### 对于企业级项目：使用 reCAPTCHA Enterprise

**优势：**
- ✅ 更强大的机器人检测
- ✅ 详细的分析报告
- ✅ 企业级支持
- ✅ 更多自定义选项
- ✅ 更好的性能

**适用场景：**
- 大型企业网站
- 高流量应用
- 需要详细分析的项目
- 有预算的商业项目

## 🔧 当前项目状态

你的项目现在使用的是**标准 reCAPTCHA v3**，配置如下：

```env
# ✅ 当前配置（标准 reCAPTCHA v3）
RECAPTCHA_SECRET_KEY=6LfdfIErAAAAAOYyaMC7M8yxIz7XNRXWC0Ad-6I_
RECAPTCHA_SITE_KEY=6LfdfIErAAAAAGPd9T0wNi37IOhF4oH0jAeObqEU
RECAPTCHA_MIN_SCORE=0.5
```

## 🚀 测试结果

测试显示系统正常工作：
- ✅ 无 token 请求被正确拒绝
- ✅ 无效 token 请求被正确拒绝
- ✅ 错误信息清晰明确
- ✅ 日志记录正常

## 📝 下一步

1. **前端集成**：在你的前端应用中使用相同的 Site Key
2. **真实测试**：使用前端获取真实的 reCAPTCHA token 进行测试
3. **生产部署**：确保生产环境使用正确的域名配置

## 🔄 如果需要切换到 Enterprise

如果将来需要切换到 reCAPTCHA Enterprise，我可以帮你：

1. 在 Google Cloud Console 创建 Enterprise 站点密钥
2. 修改代码使用 Enterprise API
3. 更新环境变量配置
4. 测试验证功能

但对于当前项目，标准 reCAPTCHA v3 已经完全够用了！🎉
