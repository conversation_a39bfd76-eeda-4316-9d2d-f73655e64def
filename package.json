{"name": "mcms", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "seed:example": "node ./scripts/seed.js", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry"}, "dependencies": {"@google-cloud/recaptcha-enterprise": "^6.3.0", "@strapi/plugin-cloud": "5.16.0", "@strapi/plugin-users-permissions": "5.16.0", "@strapi/strapi": "5.16.0", "axios": "^1.10.0", "better-sqlite3": "11.3.0", "fs-extra": "^10.0.0", "mime-types": "^2.1.27", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "styled-components": "^6.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "acbcecb1afd9f98a46e4ab2fc685231f80757dcb5c7f65a3c2fee164ba96c802"}}