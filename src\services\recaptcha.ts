import axios from 'axios';

interface RecaptchaResponse {
  success: boolean;
  score?: number;
  action?: string;
  challenge_ts?: string;
  hostname?: string;
  'error-codes'?: string[];
}

interface RecaptchaVerificationResult {
  success: boolean;
  score?: number;
  action?: string;
  error?: string;
}

/**
 * reCAPTCHA v3 验证服务 (标准版)
 */
export class RecaptchaService {
  private readonly secretKey: string;
  private readonly minScore: number;
  private readonly verifyUrl = 'https://www.google.com/recaptcha/api/siteverify';

  constructor() {
    this.secretKey = process.env.RECAPTCHA_SECRET_KEY;
    this.minScore = parseFloat(process.env.RECAPTCHA_MIN_SCORE || '0.5');

    if (!this.secretKey) {
      throw new Error('RECAPTCHA_SECRET_KEY environment variable is required');
    }
  }

  /**
   * 验证 reCAPTCHA v3 token
   * @param token - 前端传来的 reCAPTCHA token
   * @param expectedAction - 期望的 action（可选）
   * @param remoteIp - 用户 IP 地址（可选）
   * @returns 验证结果
   */
  async verifyToken(
    token: string,
    expectedAction?: string,
    remoteIp?: string
  ): Promise<RecaptchaVerificationResult> {
    if (!token) {
      return {
        success: false,
        error: 'reCAPTCHA token is required'
      };
    }

    try {
      // 构建请求参数
      const params = new URLSearchParams({
        secret: this.secretKey,
        response: token
      });

      if (remoteIp) {
        params.append('remoteip', remoteIp);
      }

      // 向 Google reCAPTCHA API 发送验证请求
      const response = await axios.post<RecaptchaResponse>(
        this.verifyUrl,
        params,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          timeout: 10000 // 10秒超时
        }
      );

      const data = response.data;

      // 检查基本验证是否成功
      if (!data.success) {
        const errorCodes = data['error-codes'] || [];
        return {
          success: false,
          error: `reCAPTCHA verification failed: ${errorCodes.join(', ')}`
        };
      }

      // 检查分数（reCAPTCHA v3）
      if (data.score !== undefined && data.score < this.minScore) {
        return {
          success: false,
          score: data.score,
          error: `reCAPTCHA score too low: ${data.score} (minimum: ${this.minScore})`
        };
      }

      // 检查 action（如果提供）
      if (expectedAction && data.action !== expectedAction) {
        return {
          success: false,
          action: data.action,
          error: `reCAPTCHA action mismatch: expected '${expectedAction}', got '${data.action}'`
        };
      }

      return {
        success: true,
        score: data.score,
        action: data.action
      };

    } catch (error) {
      strapi.log.error('reCAPTCHA verification error:', error);

      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          return {
            success: false,
            error: 'reCAPTCHA verification timeout'
          };
        }

        return {
          success: false,
          error: `reCAPTCHA verification network error: ${error.message}`
        };
      }

      return {
        success: false,
        error: 'reCAPTCHA verification failed due to internal error'
      };
    }
  }

  /**
   * 获取当前配置的最小分数
   */
  getMinScore(): number {
    return this.minScore;
  }
}

// 导出单例实例
export const recaptchaService = new RecaptchaService();
