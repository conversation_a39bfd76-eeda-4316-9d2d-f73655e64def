import axios from 'axios';

interface RecaptchaEnterpriseResponse {
  name?: string;
  event?: {
    token: string;
    siteKey: string;
    userAgent?: string;
    userIpAddress?: string;
    expectedAction?: string;
  };
  riskAnalysis?: {
    score: number;
    reasons?: string[];
  };
  tokenProperties?: {
    valid: boolean;
    invalidReason?: string;
    hostname?: string;
    action?: string;
    createTime?: string;
  };
  error?: {
    code: number;
    message: string;
    status: string;
  };
}

interface RecaptchaVerificationResult {
  success: boolean;
  score?: number;
  action?: string;
  error?: string;
  valid?: boolean;
  reasons?: string[];
}

/**
 * reCAPTCHA Enterprise 验证服务 (REST API)
 */
export class RecaptchaService {
  private readonly apiKey: string;
  private readonly projectId: string;
  private readonly siteKey: string;
  private readonly minScore: number;
  private readonly verifyUrl: string;

  constructor() {
    this.apiKey = process.env.RECAPTCHA_API_KEY;
    this.projectId = process.env.RECAPTCHA_PROJECT_ID;
    this.siteKey = process.env.RECAPTCHA_SITE_KEY;
    this.minScore = parseFloat(process.env.RECAPTCHA_MIN_SCORE || '0.5');

    this.verifyUrl = `https://recaptchaenterprise.googleapis.com/v1/projects/${this.projectId}/assessments?key=${this.apiKey}`;

    if (!this.apiKey) {
      throw new Error('RECAPTCHA_API_KEY environment variable is required');
    }
    if (!this.projectId) {
      throw new Error('RECAPTCHA_PROJECT_ID environment variable is required');
    }
    if (!this.siteKey) {
      throw new Error('RECAPTCHA_SITE_KEY environment variable is required');
    }
  }

  /**
   * 验证 reCAPTCHA Enterprise token (REST API)
   * @param token - 前端传来的 reCAPTCHA token
   * @param expectedAction - 期望的 action（可选）
   * @param remoteIp - 用户 IP 地址（可选）
   * @param userAgent - 用户代理字符串（可选）
   * @returns 验证结果
   */
  async verifyToken(
    token: string,
    expectedAction?: string,
    remoteIp?: string,
    userAgent?: string
  ): Promise<RecaptchaVerificationResult> {
    if (!token) {
      return {
        success: false,
        error: 'reCAPTCHA token is required'
      };
    }

    try {
      // 构建 Enterprise API 请求体
      const requestBody = {
        event: {
          token: token,
          expectedAction: expectedAction || 'USER_ACTION',
          siteKey: this.siteKey,
          userIpAddress: remoteIp,
          userAgent: userAgent
        }
      };

      // 向 Google reCAPTCHA Enterprise API 发送验证请求
      const response = await axios.post<RecaptchaEnterpriseResponse>(
        this.verifyUrl,
        requestBody,
        {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 10000 // 10秒超时
        }
      );

      const data = response.data;

      // 检查是否有错误
      if (data.error) {
        return {
          success: false,
          error: `reCAPTCHA Enterprise API error: ${data.error.message} (${data.error.code})`
        };
      }

      // 检查 token 是否有效
      if (!data.tokenProperties?.valid) {
        return {
          success: false,
          error: `reCAPTCHA token invalid: ${data.tokenProperties?.invalidReason || 'unknown reason'}`,
          valid: false
        };
      }

      // 获取风险分析结果
      const score = data.riskAnalysis?.score;
      const reasons = data.riskAnalysis?.reasons || [];
      const action = data.tokenProperties?.action;

      // 检查分数
      if (score !== undefined && score < this.minScore) {
        return {
          success: false,
          score: score,
          reasons: reasons,
          error: `reCAPTCHA score too low: ${score} (minimum: ${this.minScore})`
        };
      }

      // 检查 action（如果提供）
      if (expectedAction && action !== expectedAction) {
        return {
          success: false,
          action: action,
          error: `reCAPTCHA action mismatch: expected '${expectedAction}', got '${action}'`
        };
      }

      return {
        success: true,
        score: score,
        action: action,
        valid: true,
        reasons: reasons
      };

    } catch (error) {
      strapi.log.error('reCAPTCHA Enterprise verification error:', error);

      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          return {
            success: false,
            error: 'reCAPTCHA verification timeout'
          };
        }

        // 处理 API 错误响应
        if (error.response?.data) {
          const errorData = error.response.data;
          return {
            success: false,
            error: `reCAPTCHA Enterprise API error: ${errorData.error?.message || error.message}`
          };
        }

        return {
          success: false,
          error: `reCAPTCHA verification network error: ${error.message}`
        };
      }

      return {
        success: false,
        error: 'reCAPTCHA verification failed due to internal error'
      };
    }
  }

  /**
   * 获取当前配置的最小分数
   */
  getMinScore(): number {
    return this.minScore;
  }
}

// 导出单例实例
export const recaptchaService = new RecaptchaService();
