import { RecaptchaEnterpriseServiceClient } from '@google-cloud/recaptcha-enterprise';

interface RecaptchaVerificationResult {
  success: boolean;
  score?: number;
  action?: string;
  error?: string;
  valid?: boolean;
  reasons?: any[];
}

/**
 * reCAPTCHA Enterprise 验证服务
 */
export class RecaptchaService {
  private readonly projectId: string;
  private readonly siteKey: string;
  private readonly minScore: number;
  private client: RecaptchaEnterpriseServiceClient;

  constructor() {
    this.projectId = process.env.RECAPTCHA_PROJECT_ID;
    this.siteKey = process.env.RECAPTCHA_SITE_KEY;
    this.minScore = parseFloat(process.env.RECAPTCHA_MIN_SCORE || '0.5');

    if (!this.projectId) {
      throw new Error('RECAPTCHA_PROJECT_ID environment variable is required');
    }
    if (!this.siteKey) {
      throw new Error('RECAPTCHA_SITE_KEY environment variable is required');
    }

    // 初始化 reCAPTCHA Enterprise 客户端
    this.client = new RecaptchaEnterpriseServiceClient();
  }

  /**
   * 创建 reCAPTCHA Enterprise 评估
   * @param token - 前端传来的 reCAPTCHA token
   * @param expectedAction - 期望的 action（可选）
   * @param remoteIp - 用户 IP 地址（可选）
   * @param userAgent - 用户代理字符串（可选）
   * @returns 验证结果
   */
  async verifyToken(
    token: string,
    expectedAction?: string,
    remoteIp?: string,
    userAgent?: string
  ): Promise<RecaptchaVerificationResult> {
    if (!token) {
      return {
        success: false,
        error: 'reCAPTCHA token is required'
      };
    }

    try {
      // 获取项目路径
      const projectPath = this.client.projectPath(this.projectId);

      // 构建评估请求
      const request = {
        assessment: {
          event: {
            token: token,
            siteKey: this.siteKey,
            userIpAddress: remoteIp,
            userAgent: userAgent,
            expectedAction: expectedAction || 'USER_ACTION'
          }
        },
        parent: projectPath
      };

      // 创建评估
      const [response] = await this.client.createAssessment(request);

      // 检查 token 是否有效
      if (!response.tokenProperties?.valid) {
        const invalidReason = response.tokenProperties?.invalidReason || 'unknown reason';
        return {
          success: false,
          error: `reCAPTCHA token invalid: ${invalidReason}`,
          valid: false
        };
      }

      // 获取风险分析结果
      const score = response.riskAnalysis?.score;
      const reasons = response.riskAnalysis?.reasons || [];
      const action = response.tokenProperties?.action;

      // 检查分数
      if (score !== undefined && score < this.minScore) {
        return {
          success: false,
          score: score,
          reasons: reasons,
          error: `reCAPTCHA score too low: ${score} (minimum: ${this.minScore})`
        };
      }

      // 检查 action（如果提供）
      if (expectedAction && action !== expectedAction) {
        return {
          success: false,
          action: action,
          error: `reCAPTCHA action mismatch: expected '${expectedAction}', got '${action}'`
        };
      }

      return {
        success: true,
        score: score,
        action: action,
        valid: true,
        reasons: reasons
      };

    } catch (error) {
      strapi.log.error('reCAPTCHA Enterprise verification error:', error);

      // 处理 Google Cloud 客户端错误
      if (error.code) {
        return {
          success: false,
          error: `reCAPTCHA Enterprise error: ${error.message} (${error.code})`
        };
      }

      return {
        success: false,
        error: 'reCAPTCHA verification failed due to internal error'
      };
    }
  }

  /**
   * 获取当前配置的最小分数
   */
  getMinScore(): number {
    return this.minScore;
  }

  /**
   * 关闭客户端连接
   */
  async close(): Promise<void> {
    try {
      await this.client.close();
    } catch (error) {
      strapi.log.warn('Error closing reCAPTCHA client:', error);
    }
  }
}

// 导出单例实例
export const recaptchaService = new RecaptchaService();
