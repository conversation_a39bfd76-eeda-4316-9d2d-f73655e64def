# reCAPTCHA Enterprise 集成文档

本文档说明如何在 Strapi v5 项目中集成 reCAPTCHA Enterprise 来保护 Contact API。

## 🔧 配置

### 1. 环境变量设置

在 `.env` 文件中添加以下配置：

```env
# reCAPTCHA Enterprise API 配置
# Google Cloud API Key (用于访问 reCAPTCHA Enterprise API)
RECAPTCHA_API_KEY=your_google_cloud_api_key_here

# Google Cloud Project ID
RECAPTCHA_PROJECT_ID=your_project_id

# reCAPTCHA Site Key (前端使用的公钥)
RECAPTCHA_SITE_KEY=your_site_key

# reCAPTCHA v3 最小分数 (0.0 到 1.0，默认: 0.5)
# 分数越高表示越可能是人类操作
RECAPTCHA_MIN_SCORE=0.5
```

### 2. 获取 reCAPTCHA Enterprise 密钥

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建或选择一个项目
3. 启用 reCAPTCHA Enterprise API
4. 创建 API 密钥（用于后端验证）
5. 在 reCAPTCHA Enterprise 中创建站点密钥（用于前端）

## 📁 文件结构

```
src/
├── services/
│   └── recaptcha.ts          # reCAPTCHA 验证服务
├── middlewares/
│   └── recaptcha.ts          # reCAPTCHA 验证中间件
└── api/contact/
    ├── controllers/
    │   └── contact.ts        # Contact 控制器（已集成 reCAPTCHA）
    └── routes/
        └── contact.ts        # Contact 路由（使用中间件）
```

## 🚀 使用方法

### 前端集成

在前端表单中集成 reCAPTCHA v3：

```javascript
// 1. 加载 reCAPTCHA 脚本
<script src="https://www.google.com/recaptcha/api.js?render=YOUR_SITE_KEY"></script>

// 2. 在表单提交时获取 token
async function submitContactForm(formData) {
  try {
    // 获取 reCAPTCHA token
    const recaptchaToken = await grecaptcha.execute('YOUR_SITE_KEY', {
      action: 'contact_form'
    });

    // 发送请求到 Strapi API
    const response = await fetch('/api/contacts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        data: formData,
        recaptchaToken: recaptchaToken  // 包含 reCAPTCHA token
      })
    });

    if (response.ok) {
      console.log('表单提交成功');
    } else {
      const error = await response.json();
      console.error('提交失败:', error);
    }
  } catch (error) {
    console.error('错误:', error);
  }
}
```

### 替代方法：通过 Header 传递

```javascript
const response = await fetch('/api/contacts', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-Recaptcha-Token': recaptchaToken  // 通过 header 传递
  },
  body: JSON.stringify({
    data: formData
  })
});
```

## 🔒 安全特性

### 验证流程

1. **Token 验证**: 检查 reCAPTCHA token 是否存在
2. **Google API 验证**: 向 Google reCAPTCHA API 验证 token
3. **分数检查**: 验证分数是否达到最小要求（默认 0.5）
4. **Action 验证**: 验证 action 是否匹配（可选）
5. **IP 记录**: 记录请求 IP 用于日志分析

### 错误处理

系统提供详细的错误响应：

```json
{
  "error": {
    "status": 400,
    "name": "BadRequestError",
    "message": "Security verification failed",
    "details": {
      "error": "RECAPTCHA_SCORE_TOO_LOW",
      "message": "Security check failed due to suspicious activity",
      "code": "SECURITY_CHECK_FAILED",
      "score": 0.3,
      "minScore": 0.5
    }
  }
}
```

## 🧪 测试

### 运行测试脚本

```bash
# 确保 Strapi 服务器正在运行
npm run develop

# 在另一个终端运行测试
node test-recaptcha.js
```

### 手动测试

1. **无 token 测试**: 直接发送请求不包含 reCAPTCHA token
2. **无效 token 测试**: 发送包含无效 token 的请求
3. **有效 token 测试**: 从前端获取真实 token 进行测试

## 📊 日志监控

系统会记录详细的验证日志：

```javascript
// 验证成功日志
strapi.log.info('reCAPTCHA verification successful:', {
  score: 0.9,
  action: 'contact_form',
  ip: '***********'
});

// 验证失败日志
strapi.log.warn('reCAPTCHA verification failed:', {
  error: 'score too low',
  score: 0.3,
  ip: '***********',
  userAgent: 'Mozilla/5.0...'
});
```

## 🔧 自定义配置

### 为其他 API 添加 reCAPTCHA

```typescript
// 在其他路由中使用
import { recaptchaMiddlewares } from '../../../middlewares/recaptcha';

export default factories.createCoreRouter('api::other.other', {
  config: {
    create: {
      middlewares: [recaptchaMiddlewares.genericForm],
    },
  },
});
```

### 创建自定义中间件

```typescript
import { createRecaptchaMiddleware } from '../../../middlewares/recaptcha';

const customMiddleware = createRecaptchaMiddleware({
  expectedAction: 'custom_action',
  skipForAdmin: false,
  customErrorMessage: 'Custom verification required'
});
```

## 🚨 注意事项

1. **密钥安全**: 永远不要在前端代码中暴露服务端密钥
2. **分数调整**: 根据实际使用情况调整最小分数
3. **日志监控**: 定期检查验证日志，识别异常模式
4. **备用方案**: 考虑在 reCAPTCHA 服务不可用时的降级策略
5. **性能影响**: reCAPTCHA 验证会增加请求延迟

## 🔄 故障排除

### 常见问题

1. **"RECAPTCHA_SECRET_KEY environment variable is required"**
   - 检查 `.env` 文件中是否设置了正确的密钥

2. **"reCAPTCHA verification timeout"**
   - 检查网络连接和防火墙设置

3. **"reCAPTCHA score too low"**
   - 调整 `RECAPTCHA_MIN_SCORE` 或检查前端实现

4. **"reCAPTCHA action mismatch"**
   - 确保前端和后端的 action 名称一致

### 调试模式

临时降低最小分数进行调试：

```env
RECAPTCHA_MIN_SCORE=0.1
```

## 📈 生产环境建议

1. 设置适当的最小分数（建议 0.5-0.7）
2. 启用日志监控和告警
3. 定期检查和更新 reCAPTCHA 密钥
4. 考虑实施速率限制作为额外保护
5. 监控 API 响应时间和成功率
