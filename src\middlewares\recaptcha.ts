import { recaptchaService } from '../services/recaptcha';

/**
 * reCAPTCHA 验证中间件配置选项
 */
interface RecaptchaMiddlewareOptions {
  expectedAction?: string;
  skipForAdmin?: boolean;
  customErrorMessage?: string;
}

/**
 * 创建 reCAPTCHA 验证中间件
 * @param options - 中间件配置选项
 * @returns Koa 中间件函数
 */
export function createRecaptchaMiddleware(options: RecaptchaMiddlewareOptions = {}) {
  const {
    expectedAction,
    skipForAdmin = true,
    customErrorMessage
  } = options;

  return async (ctx, next) => {
    try {
      // 如果是管理员用户且配置为跳过验证，则直接通过
      if (skipForAdmin && ctx.state.user && ctx.state.user.role?.type === 'admin') {
        return await next();
      }

      // 获取 reCAPTCHA token
      const recaptchaToken = ctx.request.body.recaptchaToken || 
                            ctx.request.headers['x-recaptcha-token'];

      if (!recaptchaToken) {
        return ctx.badRequest(customErrorMessage || 'reCAPTCHA verification is required', {
          error: 'RECAPTCHA_TOKEN_MISSING',
          message: 'Please complete the reCAPTCHA verification',
          code: 'RECAPTCHA_REQUIRED'
        });
      }

      // 获取用户 IP 地址
      const remoteIp = getClientIp(ctx);

      // 执行 reCAPTCHA 验证
      const verificationResult = await recaptchaService.verifyToken(
        recaptchaToken,
        expectedAction,
        remoteIp
      );

      if (!verificationResult.success) {
        // 记录验证失败日志
        strapi.log.warn('reCAPTCHA verification failed:', {
          error: verificationResult.error,
          score: verificationResult.score,
          action: verificationResult.action,
          expectedAction,
          ip: remoteIp,
          userAgent: ctx.request.headers['user-agent'],
          url: ctx.request.url
        });

        // 根据错误类型返回不同的错误信息
        const errorResponse = getErrorResponse(verificationResult, customErrorMessage);
        return ctx.badRequest(errorResponse.message, errorResponse.details);
      }

      // 验证成功，记录日志
      strapi.log.info('reCAPTCHA verification successful:', {
        score: verificationResult.score,
        action: verificationResult.action,
        expectedAction,
        ip: remoteIp
      });

      // 将验证结果添加到上下文中，供后续使用
      ctx.state.recaptcha = {
        verified: true,
        score: verificationResult.score,
        action: verificationResult.action
      };

      await next();

    } catch (error) {
      strapi.log.error('reCAPTCHA middleware error:', error);
      
      return ctx.internalServerError('Verification service temporarily unavailable', {
        error: 'RECAPTCHA_SERVICE_ERROR',
        message: 'Please try again in a few moments',
        code: 'SERVICE_UNAVAILABLE'
      });
    }
  };
}

/**
 * 获取客户端 IP 地址
 */
function getClientIp(ctx): string {
  return ctx.request.ip || 
         ctx.request.headers['x-forwarded-for'] || 
         ctx.request.headers['x-real-ip'] || 
         ctx.request.headers['cf-connecting-ip'] || // Cloudflare
         ctx.request.headers['x-client-ip'] ||
         ctx.request.connection?.remoteAddress ||
         'unknown';
}

/**
 * 根据验证结果生成错误响应
 */
function getErrorResponse(verificationResult, customErrorMessage?: string) {
  const baseMessage = customErrorMessage || 'Security verification failed';
  
  // 根据错误类型提供更具体的错误信息
  if (verificationResult.error?.includes('score too low')) {
    return {
      message: `${baseMessage}. Please try again.`,
      details: {
        error: 'RECAPTCHA_SCORE_TOO_LOW',
        message: 'Security check failed due to suspicious activity',
        code: 'SECURITY_CHECK_FAILED',
        score: verificationResult.score,
        minScore: recaptchaService.getMinScore()
      }
    };
  }

  if (verificationResult.error?.includes('action mismatch')) {
    return {
      message: `${baseMessage}. Invalid request context.`,
      details: {
        error: 'RECAPTCHA_ACTION_MISMATCH',
        message: 'Request context validation failed',
        code: 'INVALID_CONTEXT'
      }
    };
  }

  if (verificationResult.error?.includes('timeout')) {
    return {
      message: 'Verification service timeout. Please try again.',
      details: {
        error: 'RECAPTCHA_TIMEOUT',
        message: 'Verification service is temporarily slow',
        code: 'SERVICE_TIMEOUT'
      }
    };
  }

  if (verificationResult.error?.includes('network error')) {
    return {
      message: 'Verification service unavailable. Please try again.',
      details: {
        error: 'RECAPTCHA_NETWORK_ERROR',
        message: 'Unable to connect to verification service',
        code: 'SERVICE_UNAVAILABLE'
      }
    };
  }

  // 通用错误响应
  return {
    message: `${baseMessage}. Please refresh the page and try again.`,
    details: {
      error: 'RECAPTCHA_VERIFICATION_FAILED',
      message: verificationResult.error || 'Verification failed',
      code: 'VERIFICATION_FAILED'
    }
  };
}

/**
 * 预定义的中间件实例
 */
export const recaptchaMiddlewares = {
  // 联系表单验证中间件
  contactForm: createRecaptchaMiddleware({
    expectedAction: 'contact_form',
    customErrorMessage: 'Please complete the security verification to submit your message'
  }),

  // 通用表单验证中间件
  genericForm: createRecaptchaMiddleware({
    customErrorMessage: 'Security verification is required'
  }),

  // 严格验证中间件（不跳过管理员）
  strict: createRecaptchaMiddleware({
    skipForAdmin: false,
    customErrorMessage: 'Security verification is required for all users'
  })
};
