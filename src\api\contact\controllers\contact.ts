/**
 * contact controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::contact.contact', ({ strapi }) => ({
  /**
   * 创建联系人记录（带 reCAPTCHA 验证）
   * 注意：reCAPTCHA 验证由路由中间件处理
   */
  async create(ctx) {
    try {
      // reCAPTCHA 验证已由中间件完成，验证信息存储在 ctx.state.recaptcha 中
      const recaptchaInfo = ctx.state.recaptcha;

      // 调用原始的 create 方法创建联系人记录
      const response = await super.create(ctx);

      // 在响应中添加验证信息（可选）
      if (response && response.data && recaptchaInfo) {
        response.meta = {
          ...response.meta,
          security: {
            verified: recaptchaInfo.verified,
            score: recaptchaInfo.score,
            timestamp: new Date().toISOString()
          }
        };
      }

      // 记录成功创建的日志
      strapi.log.info('Contact created successfully:', {
        id: response?.data?.id,
        recaptchaScore: recaptchaInfo?.score,
        ip: ctx.request.ip
      });

      return response;

    } catch (error) {
      strapi.log.error('Contact creation error:', error);

      return ctx.internalServerError('An error occurred while processing your request', {
        error: 'CONTACT_CREATION_FAILED',
        message: 'Unable to save your message. Please try again later.',
        code: 'SAVE_FAILED'
      });
    }
  }
}));
