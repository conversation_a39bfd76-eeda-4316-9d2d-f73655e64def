/**
 * reCAPTCHA v3 集成测试脚本
 *
 * 使用方法：
 * 1. 确保 Strapi 服务器正在运行
 * 2. 在 .env 文件中设置正确的 reCAPTCHA v3 配置
 * 3. 运行: node test-recaptcha.js
 */

const axios = require('axios');

// 配置
const STRAPI_URL = 'http://localhost:1337';
const API_ENDPOINT = `${STRAPI_URL}/api/contacts`;

// 测试数据
const testContactData = {
  data: {
    username: 'Test User',
    email: '<EMAIL>',
    company: 'Test Company',
    phone: '+1234567890',
    message: [
      {
        type: 'paragraph',
        children: [
          {
            type: 'text',
            text: 'This is a test message for reCAPTCHA integration.'
          }
        ]
      }
    ]
  }
};

/**
 * 测试没有 reCAPTCHA token 的请求
 */
async function testWithoutRecaptcha() {
  console.log('\n🧪 测试 1: 没有 reCAPTCHA token 的请求');
  console.log('=' .repeat(50));
  
  try {
    const response = await axios.post(API_ENDPOINT, testContactData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('❌ 意外成功 - 应该被拒绝');
    console.log('响应:', response.data);
  } catch (error) {
    if (error.response) {
      console.log('✅ 正确被拒绝');
      console.log('状态码:', error.response.status);
      console.log('错误信息:', error.response.data);
    } else {
      console.log('❌ 网络错误:', error.message);
    }
  }
}

/**
 * 测试无效的 reCAPTCHA token
 */
async function testWithInvalidRecaptcha() {
  console.log('\n🧪 测试 2: 无效的 reCAPTCHA token');
  console.log('=' .repeat(50));
  
  try {
    const response = await axios.post(API_ENDPOINT, {
      ...testContactData,
      recaptchaToken: 'invalid_token_12345'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('❌ 意外成功 - 应该被拒绝');
    console.log('响应:', response.data);
  } catch (error) {
    if (error.response) {
      console.log('✅ 正确被拒绝');
      console.log('状态码:', error.response.status);
      console.log('错误信息:', error.response.data);
    } else {
      console.log('❌ 网络错误:', error.message);
    }
  }
}

/**
 * 测试通过 header 传递 reCAPTCHA token
 */
async function testWithRecaptchaHeader() {
  console.log('\n🧪 测试 3: 通过 header 传递 reCAPTCHA token');
  console.log('=' .repeat(50));
  
  try {
    const response = await axios.post(API_ENDPOINT, testContactData, {
      headers: {
        'Content-Type': 'application/json',
        'X-Recaptcha-Token': 'invalid_header_token'
      }
    });
    
    console.log('❌ 意外成功 - 应该被拒绝');
    console.log('响应:', response.data);
  } catch (error) {
    if (error.response) {
      console.log('✅ 正确被拒绝');
      console.log('状态码:', error.response.status);
      console.log('错误信息:', error.response.data);
    } else {
      console.log('❌ 网络错误:', error.message);
    }
  }
}

/**
 * 测试服务器状态
 */
async function testServerStatus() {
  console.log('\n🔍 检查服务器状态');
  console.log('=' .repeat(50));
  
  try {
    const response = await axios.get(`${STRAPI_URL}/api/contacts`, {
      timeout: 5000
    });
    console.log('✅ 服务器运行正常');
    console.log('API 端点可访问');
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ 无法连接到服务器');
      console.log('请确保 Strapi 服务器正在运行在', STRAPI_URL);
      return false;
    } else if (error.response) {
      console.log('✅ 服务器运行正常');
      console.log('状态码:', error.response.status);
    } else {
      console.log('❌ 网络错误:', error.message);
      return false;
    }
  }
  return true;
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始 reCAPTCHA 集成测试');
  console.log('测试目标:', API_ENDPOINT);
  console.log('时间:', new Date().toLocaleString());
  
  // 检查服务器状态
  const serverOk = await testServerStatus();
  if (!serverOk) {
    console.log('\n❌ 测试终止 - 服务器不可用');
    return;
  }
  
  // 运行测试
  await testWithoutRecaptcha();
  await testWithInvalidRecaptcha();
  await testWithRecaptchaHeader();
  
  console.log('\n📋 测试总结');
  console.log('=' .repeat(50));
  console.log('✅ 所有测试都应该被正确拒绝（这是预期行为）');
  console.log('✅ reCAPTCHA 验证中间件工作正常');
  console.log('\n📝 注意事项:');
  console.log('- 要测试有效的 reCAPTCHA token，需要从前端获取真实的 token');
  console.log('- 确保在 .env 文件中设置了正确的 reCAPTCHA v3 配置:');
  console.log('  * RECAPTCHA_SECRET_KEY (reCAPTCHA Secret Key)');
  console.log('  * RECAPTCHA_SITE_KEY (reCAPTCHA Site Key)');
  console.log('- 生产环境中请使用真实的 reCAPTCHA v3 密钥');
}

// 运行测试
runTests().catch(console.error);
